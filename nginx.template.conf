# 全局配置
worker_processes auto;
# 核心修复：将 PID 文件指向 /tmp 目录，任何用户都有权限写入
pid /tmp/nginx.pid;
# 最佳实践：将错误日志输出到标准错误流，以便 PaaS 平台捕获
error_log /dev/stderr;

# 事件模块
events {
    worker_connections 1024;
}

# HTTP 模块
http {
    # 最佳实践：将访问日志输出到标准输出流
    access_log /dev/stdout;
    # 禁止在错误页面中显示 Nginx 版本号
    server_tokens off;

    # 包含 MIME 类型定义
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen ${PORT} default_server;
        listen [::]:${PORT} default_server;

        location / {
            root   /app/html;
            index  index.html;
            try_files $uri $uri/ /index.html;
        }

        location ${WSPATH} {
            if ($http_upgrade != "websocket") {
                return 404;
            }
            proxy_pass http://127.0.0.1:9008;
            proxy_redirect off;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }

        location /healthz {
            return 200 "OK";
        }
    }
}