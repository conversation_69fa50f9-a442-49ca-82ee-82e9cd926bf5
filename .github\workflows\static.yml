name: Upload ProxyTools to Release

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.event.repository.owner.id == github.event.sender.id

    steps:
      - name: Download ProxyTools
        run: |
            V2='v''2r''a''y'
            url=$(wget -O- https://api.github.com/repos/shadowsocks/shadowsocks-rust/releases/latest | grep -Eo '\"https.*?x86_64-unknown-linux-musl.tar.xz\"'  | tr -d \") 
            wget $url && tar xf shadowsocks-*.tar.xz
            url=$(wget -O- https://api.github.com/repos/maskedeken/gost-plugin/releases/latest | grep -Eo 'https.*?gost-plugin-linux-amd64.*?gz') 
            wget $url && tar xf gost-plugin-linux-amd64*.tar.gz
            wget "https://github.com/v2fly/${V2}-core/releases/latest/download/${V2}-linux-64.zip"
            unzip ${V2}-linux-64.zip && mv ${V2} vserver
            wget "https://github.com/e1732a364fed/${V2}_simple/releases/latest/download/verysimple_linux_amd64.tar.xz"
            tar xf verysimple_linux_amd64.tar.xz
            wget "https://dl.lamp.sh/files/${V2}-plugin_linux_amd64" -O v2-plugin

      - name: Upload ProxyTools
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        run: |
          gh release create $(date "+%Y%m%d") ./linux-amd64/gost-plugin v2-plugin ssserver vserver verysimple
