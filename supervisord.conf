[supervisord]
nodaemon=true
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/tmp/supervisord.pid

[program:mysql]
command=/app/mysql run -c /app/config.json
directory=/app
autostart=true
autorestart=true
startretries=3
stderr_logfile=/dev/null
stderr_logfile_maxbytes=0
stdout_logfile=/dev/null
stdout_logfile_maxbytes=0
user=nginx

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
directory=/app
autostart=true
autorestart=true
startretries=3
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
user=nginx