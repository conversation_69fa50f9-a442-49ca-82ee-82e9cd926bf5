# Enginx - PaaS 隐蔽代理服务

一个专为 PaaS 平台设计的隐蔽代理服务，采用前后端分离架构，实现 WebSocket 流量转发和静态网站伪装。

## 🎯 项目设计理念

### 核心架构
- **前端分流器**: Nginx 作为流量入口，负责路径分流和伪装
- **后端代理**: *Ray 代理服务，处理实际代理流量
- **伪装层**: 静态网站作为流量掩护，避免特征检测

### 设计原则
- **隐蔽性优先**: 代码中避免敏感关键词，适应 PaaS 平台审查
- **资源隔离**: 前后端进程分离，互不影响
- **最小暴露**: 无日志输出，减少系统指纹

## 📁 项目结构

```
├── Dockerfile              # 多阶段构建配置
├── entrypoint.sh          # 容器启动脚本，处理配置渲染
├── supervisord.conf       # 进程管理配置，协调前后端服务
├── nginx.template.conf    # Nginx 配置模板，支持环境变量替换
└── config.template.json   # 代理服务配置模板，动态生成
```

## 🔧 配置参数

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `UUID` | `a6a45391-31fe-4bdd-828c-51f02c943dce` | 代理服务客户端标识 |
| `WSPATH` | `/api/events` | WebSocket 连接路径 |
| `PORT` | `8080` | Nginx 监听端口 |
| `PROTOCOL` | `v l e s s` | 代理协议类型 |

## 🏗️ 架构设计详解

### 1. 流量处理流程
```
客户端请求 → Nginx 分流器 → {
    ├── 普通路径 → 静态网站伪装
    └── 指定路径 → WebSocket 代理服务
}
```

### 2. 进程架构
- **主进程**: supervisord 进程管理器
- **前端进程**: Nginx (nginx 用户)
- **后端进程**: *Ray 代理服务 (nginx 用户)

### 3. 配置渲染机制
- **模板化配置**: 使用 envsubst 进行环境变量替换
- **运行时生成**: 容器启动时动态生成最终配置文件
- **参数隔离**: 敏感配置通过环境变量传递

### 4. 安全设计
- **用户隔离**: 所有服务以 nginx 用户运行
- **权限最小化**: 只授予必要目录的访问权限
- **日志控制**: 代理服务完全禁用日志输出
- **流量伪装**: 使用静态网站进行流量伪装，避免特征识别

## 🔄 工作原理

1. **启动阶段**: supervisord 启动，加载前后端服务
2. **配置阶段**: entrypoint.sh 渲染配置文件
3. **运行阶段**: Nginx 监听指定端口，代理服务监听本地端口
4. **分流阶段**: 根据请求路径进行智能分流
5. **伪装阶段**: 普通访问展示静态网站内容

---

*本项目采用前后端分离设计，注重隐蔽性和可维护性，适合 PaaS 平台部署场景。*